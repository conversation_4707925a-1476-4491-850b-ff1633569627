MARKET MONITOR - DAILY SUMMARY
{{ data.date.strftime('%B %d, %Y') }}

Hello {{ user.first_name or user.username }}!

{% if data.error %}
NOTICE: We encountered an issue generating your daily summary. Please visit our website for the latest market information.
{% else %}

MARKET OVERVIEW
===============
{% if data.market_data %}
SPY Price: ${{ "%.2f"|format(data.market_data.current_price or 0) }}
{% if data.market_data.price_change %}
Change: {{ "%.2f"|format(data.market_data.price_change) }} ({{ "%.2f"|format(data.market_data.price_change_percent) }}%)
{% endif %}
Volume: {{ "{:,}"|format(data.market_data.volume or 0) }}
Day High: ${{ "%.2f"|format(data.market_data.high or 0) }}
Day Low: ${{ "%.2f"|format(data.market_data.low or 0) }}
{% else %}
Market data unavailable
{% endif %}

{% if data.llm_prediction and (data.llm_prediction.prediction or data.llm_prediction.critical_levels) %}

AI MARKET ANALYSIS
==================
{% if data.llm_prediction.prediction and data.llm_prediction.confidence %}
Market Direction: {{ data.llm_prediction.prediction.upper() }}
Confidence: {{ "%.1f"|format(data.llm_prediction.confidence * 100) }}%
{% endif %}

{% if data.llm_prediction.critical_levels %}
Critical Levels:
{% for level_name, level_value in data.llm_prediction.critical_levels.items() %}
{% if level_value is number %}
  {{ level_name.replace('_', ' ').title() }}: ${{ "%.2f"|format(level_value) }}
{% endif %}
{% endfor %}
{% endif %}

{% if data.llm_prediction.probabilities %}
Probability Breakdown:
{% for label, score in data.llm_prediction.probabilities.items() %}
  {{ label.title() }}: {{ "%.1f"|format(score * 100) }}%
{% endfor %}
{% endif %}
{% endif %}

MARKET PREDICTIONS
==================
{% if data.prediction %}
Traditional Model: {{ data.prediction.prediction_label or 'N/A' }}
Confidence: {{ "%.1f"|format((data.prediction.confidence or 0) * 100) }}%
{% endif %}

{% if data.llm_prediction %}
AI Analysis: {{ data.llm_prediction.prediction or 'N/A' }}

{% if data.llm_prediction.reasoning %}
Reasoning: {{ data.llm_prediction.reasoning[:300] }}{% if data.llm_prediction.reasoning|length > 300 %}...{% endif %}
{% endif %}
{% endif %}

KEY MARKET EVIDENCE
===================
{% if data.llm_prediction and data.llm_prediction.key_evidence and data.llm_prediction.key_evidence|length > 0 %}
{% for evidence in data.llm_prediction.key_evidence[:6] %}
{% set clean_fact = evidence.fact|regex_replace(r'\s*\[Article\s+[\d,\s]+\]\s*\.?\s*$', '', ignorecase=True) if evidence.fact else evidence %}
{{ loop.index }}. {{ clean_fact[:130] }}{% if clean_fact|length > 130 %}...{% endif %}
{% if evidence.url and evidence.url != 'N/A' and evidence.article_ref != 'MARKET' %}
   Read more: {{ evidence.url }}
{% elif evidence.article_ref == 'MARKET' or evidence.source == 'N/A' %}
   Source: Market data analysis
{% endif %}

{% endfor %}
{% elif data.llm_prediction and data.llm_prediction.evidence and data.llm_prediction.evidence|length > 0 %}
{% for evidence in data.llm_prediction.evidence[:3] %}
{{ loop.index }}. {{ evidence[:150] }}{% if evidence|length > 150 %}...{% endif %}

{% endfor %}
{% else %}
No key evidence available
{% endif %}

DOMINANT MARKET THEME
=====================
{% if data.llm_prediction and data.llm_prediction.dominant_theme and data.llm_prediction.dominant_theme.theme %}
{{ data.llm_prediction.dominant_theme.theme }}

{% if data.llm_prediction.dominant_theme.supporting_articles and data.llm_prediction.dominant_theme.supporting_articles|length > 0 %}
Supporting Articles ({{ data.llm_prediction.dominant_theme.supporting_articles|length }}):
{% for article in data.llm_prediction.dominant_theme.supporting_articles[:3] %}
{{ loop.index }}. {{ article.article_title or 'Article' }} ({{ article.source or 'Unknown' }})
{% if article.url and article.url != 'N/A' %}
   URL: {{ article.url }}
{% endif %}

{% endfor %}
{% endif %}
{% elif data.llm_prediction and data.llm_prediction.themes and data.llm_prediction.themes|length > 0 %}
Market Themes: {{ data.llm_prediction.themes[:3]|join(', ') }}
{% else %}
No dominant theme identified
{% endif %}

TOP FINANCIAL NEWS
==================
{% if data.news and data.news.all %}
{% for article in data.news.all[:5] %}
{{ loop.index }}. {{ article.title }}
   Source: {{ article.source }} | Date: {{ article.date.strftime('%B %d, %Y') if article.date else 'Recent' }}
   {% if article.content %}
   Summary: {{ article.content[:100] }}{% if article.content|length > 100 %}...{% endif %}
   {% endif %}
   URL: {{ article.url }}

{% endfor %}
{% else %}
No recent news available
{% endif %}

{% endif %}

VIEW FULL DASHBOARD
===================
Visit our website for detailed charts, analysis, and more:
{{ url_for('index', _external=True) }}

MANAGE PREFERENCES
==================
Update your email preferences or unsubscribe:
{{ url_for('auth.profile', _external=True) }}

---
This email was sent to {{ user.email }} because you subscribed to daily market summaries.
© {{ data.date.year }} Market Monitor. All rights reserved.