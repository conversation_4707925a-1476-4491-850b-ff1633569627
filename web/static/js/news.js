/**
 * News JavaScript Module
 * Yahoo Finance-style news layout with featured news and advanced search
 */

class News {
    constructor() {
        this.featuredArticles = [];
        this.moreArticles = [];
        this.displayedMoreArticles = [];
        this.moreArticlesOffset = 0;
        this.moreArticlesLimit = 10;
        this.isLoading = false;
        this.abortController = null;
        this.currentFilters = {
            search: '',
            dateFrom: '',
            dateTo: '',
            source: '',
            sentiment: ''
        };
        
        // Debounce search function
        this.debouncedSearch = this.debounce(this.performSearch.bind(this), 300);
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadNews();
    }

    setupEventListeners() {
        // Search functionality with debouncing
        const searchInput = document.getElementById('news-search-input');
        const searchButton = document.getElementById('search-button');
        
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });
            
            // Add real-time search with debouncing
            searchInput.addEventListener('input', (e) => {
                if (e.target.value.trim() !== this.currentFilters.search) {
                    this.debouncedSearch();
                }
            });
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }

        // Advanced search toggle
        const toggleAdvanced = document.getElementById('toggle-advanced-search');
        const advancedFilters = document.getElementById('advanced-filters');
        
        if (toggleAdvanced && advancedFilters) {
            toggleAdvanced.addEventListener('click', (e) => {
                e.preventDefault();
                advancedFilters.classList.toggle('show');
                const icon = toggleAdvanced.querySelector('i');
                if (icon) {
                    if (advancedFilters.classList.contains('show')) {
                        icon.className = 'bi bi-chevron-up';
                        toggleAdvanced.setAttribute('aria-expanded', 'true');
                    } else {
                        icon.className = 'bi bi-sliders';
                        toggleAdvanced.setAttribute('aria-expanded', 'false');
                    }
                }
            });
        }

        // Filter controls
        const applyFiltersBtn = document.getElementById('apply-filters');
        const clearFiltersBtn = document.getElementById('clear-filters');
        
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.applyFilters();
            });
        }
        
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearFilters();
            });
        }
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.abortController) {
                this.abortController.abort();
            }
        });
    }

    async loadNews() {
        if (this.isLoading) {
            console.log('News loading already in progress');
            return;
        }
        
        try {
            this.isLoading = true;
            this.abortController = new AbortController();
            this.showLoading();

            // Load featured news and more news separately with timeout
            await Promise.allSettled([
                this.loadFeaturedNews(),
                this.loadMoreNews()
            ]);

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Error loading news:', error);
                this.showError(this.sanitizeErrorMessage(error.message));
            }
        } finally {
            this.isLoading = false;
            this.abortController = null;
        }
    }

    async loadFeaturedNews() {
        try {
            const response = await fetch('/api/featured-news?limit=6', {
                signal: this.abortController?.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('Featured news endpoint not found');
                } else if (response.status >= 500) {
                    throw new Error('Server error occurred');
                } else {
                    const data = await response.json().catch(() => ({}));
                    throw new Error(data.error || `Request failed with status ${response.status}`);
                }
            }
            
            const data = await response.json();
            this.featuredArticles = Array.isArray(data.all) ? data.all : [];
            this.renderFeaturedNews();

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Featured news request aborted');
                return;
            }
            
            console.error('Error loading featured news:', error);
            
            // Fallback to regular news API
            try {
                const fallbackResponse = await fetch('/api/news?limit=6', {
                    signal: this.abortController?.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (fallbackResponse.ok) {
                    const fallbackData = await fallbackResponse.json();
                    this.featuredArticles = Array.isArray(fallbackData.all) ? fallbackData.all : [];
                    this.renderFeaturedNews();
                } else {
                    throw new Error('Fallback request also failed');
                }
            } catch (fallbackError) {
                if (fallbackError.name !== 'AbortError') {
                    this.showFeaturedError(this.sanitizeErrorMessage(error.message));
                }
            }
        }
    }

    async loadMoreNews() {
        try {
            // Get IDs of featured articles to exclude
            const excludeIds = this.featuredArticles
                .filter(article => article && article.id)
                .map(article => article.id);
                
            const params = new URLSearchParams({
                limit: '20'
            });

            // Add exclude IDs as separate parameters
            excludeIds.forEach(id => {
                if (id != null) {
                    params.append('exclude_ids', String(id));
                }
            });

            const response = await fetch(`/api/more-news?${params.toString()}`, {
                signal: this.abortController?.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('More news endpoint not found');
                } else if (response.status >= 500) {
                    throw new Error('Server error occurred');
                } else {
                    const data = await response.json().catch(() => ({}));
                    throw new Error(data.error || `Request failed with status ${response.status}`);
                }
            }
            
            const data = await response.json();
            this.moreArticles = Array.isArray(data.all) ? data.all : [];
            this.renderMoreNews();

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('More news request aborted');
                return;
            }
            
            console.error('Error loading more news:', error);
            
            // Fallback to regular news API
            try {
                const fallbackResponse = await fetch('/api/news?limit=20', {
                    signal: this.abortController?.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (fallbackResponse.ok) {
                    const fallbackData = await fallbackResponse.json();
                    this.moreArticles = Array.isArray(fallbackData.all) ? fallbackData.all : [];
                    this.renderMoreNews();
                } else {
                    throw new Error('Fallback request also failed');
                }
            } catch (fallbackError) {
                if (fallbackError.name !== 'AbortError') {
                    this.showMoreNewsError(this.sanitizeErrorMessage(error.message));
                }
            }
        }
    }



    renderFeaturedNews() {
        const mainContainer = document.querySelector('.main-featured-container');
        const rightSideContainer = document.querySelector('.right-side-featured-container');
        const bottomContainer = document.querySelector('.bottom-featured-container');

        if (!mainContainer || !rightSideContainer || !bottomContainer) {
            console.warn('Featured news containers not found');
            return;
        }

        if (!Array.isArray(this.featuredArticles) || this.featuredArticles.length === 0) {
            mainContainer.innerHTML = '<div class="news-error">No featured news available</div>';
            rightSideContainer.innerHTML = '';
            bottomContainer.innerHTML = '';
            return;
        }

        // Main featured article (first one)
        const mainArticle = this.featuredArticles[0];
        
        if (!mainArticle) {
            mainContainer.innerHTML = '<div class="news-error">No main article available</div>';
            return;
        }
        
        try {
            const mainSentiment = this.getSentimentFromArticle(mainArticle);
            const mainSentimentClass = mainSentiment ? `sentiment-${this.sanitizeClassName(mainSentiment.toLowerCase())}` : '';
            const mainSentimentBadge = mainSentiment ? `<span class="sentiment-badge ${mainSentimentClass}">${this.escapeHtml(mainSentiment)}</span>` : '';

            mainContainer.innerHTML = this.createArticleHtml({
                article: mainArticle,
                className: 'main-featured-article',
                sentimentBadge: mainSentimentBadge,
                contentLength: 250,
                showInfluence: true
            });
        } catch (error) {
            console.error('Error rendering main article:', error);
            mainContainer.innerHTML = '<div class="news-error">Error displaying main article</div>';
        }

        // Right side featured articles (next 2)
        const rightSideArticles = this.featuredArticles.slice(1, 3).filter(Boolean);
        
        try {
            const rightSideHtml = rightSideArticles.map(article => {
                const sentiment = this.getSentimentFromArticle(article);
                const sentimentClass = sentiment ? `sentiment-${this.sanitizeClassName(sentiment.toLowerCase())}` : '';
                const sentimentBadge = sentiment ? `<span class="sentiment-badge ${sentimentClass}">${this.escapeHtml(sentiment)}</span>` : '';

                return this.createArticleHtml({
                    article,
                    className: 'side-featured-article',
                    sentimentBadge,
                    contentLength: 100,
                    showInfluence: true
                });
            }).join('');

            rightSideContainer.innerHTML = rightSideHtml;
        } catch (error) {
            console.error('Error rendering right side articles:', error);
            rightSideContainer.innerHTML = '<div class="news-error">Error displaying side articles</div>';
        }

        // Bottom featured articles (next 3)
        const bottomArticles = this.featuredArticles.slice(3, 6).filter(Boolean);
        
        try {
            const bottomHtml = bottomArticles.map(article => {
                const sentiment = this.getSentimentFromArticle(article);
                const sentimentClass = sentiment ? `sentiment-${this.sanitizeClassName(sentiment.toLowerCase())}` : '';
                const sentimentBadge = sentiment ? `<span class="sentiment-badge ${sentimentClass}">${this.escapeHtml(sentiment)}</span>` : '';

                return this.createArticleHtml({
                    article,
                    className: 'bottom-featured-article',
                    sentimentBadge,
                    contentLength: 100,
                    showInfluence: true
                });
            }).join('');

            bottomContainer.innerHTML = bottomHtml;
        } catch (error) {
            console.error('Error rendering bottom articles:', error);
            bottomContainer.innerHTML = '<div class="news-error">Error displaying bottom articles</div>';
        }
    }

    renderMoreNews() {
        const container = document.getElementById('more-news-list');
        if (!container) return;

        if (this.moreArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No more news available</div>';
            return;
        }

        // Load initial batch
        this.loadMoreArticles(true);
    }

    loadMoreArticles(reset = false) {
        const container = document.getElementById('more-news-list');
        if (!container) {
            console.warn('More news container not found');
            return;
        }

        if (reset) {
            this.moreArticlesOffset = 0;
            this.displayedMoreArticles = [];
            container.innerHTML = '';
        }

        if (!Array.isArray(this.moreArticles)) {
            container.innerHTML = '<div class="news-error">No more news available</div>';
            return;
        }

        // Get next batch of articles
        const nextBatch = this.moreArticles.slice(
            this.moreArticlesOffset,
            this.moreArticlesOffset + this.moreArticlesLimit
        ).filter(Boolean);

        if (nextBatch.length === 0 && this.displayedMoreArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No more news available</div>';
            return;
        }

        // Add articles to displayed list
        this.displayedMoreArticles.push(...nextBatch);
        this.moreArticlesOffset += nextBatch.length;

        try {
            // Render articles
            const articlesHtml = this.displayedMoreArticles.map(article => {
                const sentiment = this.getSentimentFromArticle(article);
                const sentimentBadge = sentiment ? 
                    `<span class="sentiment-badge sentiment-${this.sanitizeClassName(sentiment.toLowerCase())}">${this.escapeHtml(sentiment)}</span>` : '';

                const imageHtml = article.image_url ?
                    `<img src="${this.escapeHtml(article.image_url)}" alt="${this.escapeHtml(article.title || 'News image')}" class="news-list-image" onerror="this.style.display='none'" loading="lazy">` :
                    `<div class="news-list-image"></div>`;
                    
                const contentHtml = article.content ?
                    `<p class="news-list-summary">${this.escapeHtml(article.content.substring(0, 250))}...</p>` :
                    '';

                return `
                    <div class="news-list-item" onclick="this.openArticle('${this.escapeHtml(article.url || '')}')" role="button" tabindex="0" onkeypress="if(event.key==='Enter') this.click()">
                        ${imageHtml}
                        <div class="news-list-content">
                            <h6 class="news-list-title">${this.escapeHtml(article.title || 'Untitled')}</h6>
                            <div class="news-list-meta">
                                <span class="news-list-source">${this.escapeHtml(article.source || 'Unknown')}</span>
                                <span>•</span>
                                <span class="news-list-date">${this.formatDateWithTime(article.date || article.publish_time)}</span>
                                ${sentimentBadge}
                            </div>
                            ${contentHtml}
                        </div>
                    </div>
                `;
            }).join('');

            // Add load more button if there are more articles
            const loadMoreHtml = this.moreArticlesOffset < this.moreArticles.length ?
                `<div class="load-more-container">
                    <button class="load-more-btn" onclick="window.news?.loadMoreArticles()" type="button">
                        Load More Articles
                    </button>
                </div>` : '';

            container.innerHTML = articlesHtml + loadMoreHtml;
        } catch (error) {
            console.error('Error rendering more articles:', error);
            container.innerHTML = '<div class="news-error">Error displaying articles</div>';
        }
    }

    performSearch() {
        const searchInput = document.getElementById('news-search-input');
        if (searchInput) {
            this.currentFilters.search = searchInput.value.trim();
            this.applyFilters();
        }
    }

    applyFilters() {
        // Get filter values
        this.currentFilters.dateFrom = document.getElementById('date-from')?.value || '';
        this.currentFilters.dateTo = document.getElementById('date-to')?.value || '';
        this.currentFilters.source = document.getElementById('news-source')?.value || '';
        this.currentFilters.sentiment = document.getElementById('sentiment-filter')?.value || '';

        // Build API parameters
        const params = new URLSearchParams();
        if (this.currentFilters.search) params.append('search', this.currentFilters.search);
        if (this.currentFilters.dateFrom) params.append('start_date', this.currentFilters.dateFrom);
        if (this.currentFilters.dateTo) params.append('end_date', this.currentFilters.dateTo);
        params.append('limit', '50');

        // Fetch filtered news
        this.loadFilteredNews(params);
    }

    async loadFilteredNews(params) {
        try {
            this.showLoading();
            
            const response = await fetch(`/api/news?${params.toString()}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Failed to load filtered news');
            }
            
            this.allArticles = data.all || [];
            
            // Apply client-side filters
            this.allArticles = this.allArticles.filter(article => {
                // Source filter
                if (this.currentFilters.source && article.source.toLowerCase() !== this.currentFilters.source.toLowerCase()) {
                    return false;
                }
                
                // Sentiment filter
                if (this.currentFilters.sentiment) {
                    const sentiment = this.getSentimentFromArticle(article);
                    if (!sentiment || sentiment.toLowerCase() !== this.currentFilters.sentiment.toLowerCase()) {
                        return false;
                    }
                }
                
                return true;
            });
            
            this.processArticles();
            this.renderNews();
            
        } catch (error) {
            console.error('Error loading filtered news:', error);
            this.showError(error.message);
        }
    }

    clearFilters() {
        try {
            // Clear all filter inputs safely
            const elements = [
                'news-search-input',
                'date-from', 
                'date-to',
                'news-source',
                'sentiment-filter'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = '';
                }
            });
            
            // Reset filters and reload
            this.currentFilters = {
                search: '',
                dateFrom: '',
                dateTo: '',
                source: '',
                sentiment: ''
            };
            
            this.loadNews();
        } catch (error) {
            console.error('Error clearing filters:', error);
        }
    }

    getSentimentFromArticle(article) {
        if (article.article_metadata && article.article_metadata.sentiment_analysis) {
            return article.article_metadata.sentiment_analysis.label;
        }
        return null;
    }

    formatDate(dateString) {
        if (!dateString) return 'Recent';

        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return '1 day ago';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;

        return date.toLocaleDateString();
    }

    formatDateWithTime(dateString) {
        if (!dateString) return 'Recent';

        const date = new Date(dateString);

        // Convert to EST timezone
        const estDate = new Date(date.toLocaleString("en-US", {timeZone: "America/New_York"}));

        const options = {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
            timeZone: 'America/New_York'
        };

        return estDate.toLocaleDateString('en-US', options) + ' EST';
    }

    renderInfluenceTagging(article, compact = false) {
        try {
            if (!article?.article_metadata?.influence_tagging) {
                return '';
            }

            const influence = article.article_metadata.influence_tagging;
            const tags = Array.isArray(influence.tags) ? influence.tags : [];
            const category = this.escapeHtml(influence.category || '');
            const regions = Array.isArray(influence.regions) ? influence.regions : [];
            const score = typeof influence.influence === 'number' ? influence.influence : 0;
            const reason = this.escapeHtml(influence.reason || '');

            if (compact) {
                // Compact version for side articles
                const scoreClass = score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral';
                const tooltipAttr = reason ? `data-tooltip="${reason}"` : '';
                const tooltipClass = reason ? 'has-tooltip' : '';
                
                return `
                    <div class="influence-tags compact">
                        <div class="influence-meta">
                            <span>${category}</span>
                            <span class="influence-score ${scoreClass} ${tooltipClass}" ${tooltipAttr}>${score > 0 ? '+' : ''}${score}</span>
                        </div>
                    </div>
                `;
            }

            // Full version for main article
            const scoreClass = score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral';
            const tagsHtml = tags.slice(0, 5).map(tag => `<span class="influence-tag">${this.escapeHtml(tag)}</span>`).join('');
            const regionsText = regions.map(region => this.escapeHtml(region)).join(', ');
            const tooltipAttr = reason ? `data-tooltip="${reason}"` : '';
            const tooltipClass = reason ? 'has-tooltip' : '';

            return `
                <div class="influence-tags">
                    <div class="influence-tags-list">
                        ${tagsHtml}
                    </div>
                    <div class="influence-meta">
                        <span><strong>Category:</strong> ${category} | <strong>Region:</strong> ${regionsText}</span>
                        <span class="influence-score ${scoreClass} ${tooltipClass}" ${tooltipAttr}>Influence: ${score > 0 ? '+' : ''}${score}</span>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('Error rendering influence tagging:', error);
            return '';
        }
    }

    showLoading() {
        const featuredContainer = document.getElementById('featured-news-grid');
        const moreContainer = document.getElementById('more-news-list');
        
        const loadingHtml = `
            <div class="news-loading">
                <div class="news-loading-spinner"></div>
                <p>Loading news...</p>
            </div>
        `;
        
        if (featuredContainer) featuredContainer.innerHTML = loadingHtml;
        if (moreContainer) moreContainer.innerHTML = loadingHtml;
    }

    showError(message) {
        this.showFeaturedError(message);
        this.showMoreNewsError(message);
    }

    showFeaturedError(message) {
        const mainContainer = document.querySelector('.main-featured-container');
        const rightSideContainer = document.querySelector('.right-side-featured-container');
        const bottomContainer = document.querySelector('.bottom-featured-container');

        const errorHtml = `
            <div class="news-error">
                <i class="bi bi-exclamation-triangle"></i>
                Error loading featured news: ${this.escapeHtml(message)}
            </div>
        `;

        if (mainContainer) mainContainer.innerHTML = errorHtml;
        if (rightSideContainer) rightSideContainer.innerHTML = '';
        if (bottomContainer) bottomContainer.innerHTML = '';
    }

    showMoreNewsError(message) {
        const moreContainer = document.getElementById('more-news-list');

        const errorHtml = `
            <div class="news-error">
                <i class="bi bi-exclamation-triangle"></i>
                Error loading more news: ${this.escapeHtml(message)}
            </div>
        `;

        if (moreContainer) moreContainer.innerHTML = errorHtml;
    }
    
    // Utility functions for improved security and functionality
    escapeHtml(text) {
        if (typeof text !== 'string') return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    sanitizeClassName(className) {
        if (typeof className !== 'string') return '';
        return className.replace(/[^a-z0-9-_]/gi, '');
    }
    
    sanitizeErrorMessage(message) {
        if (typeof message !== 'string') return 'Unknown error occurred';
        // Remove potentially sensitive information
        return message.replace(/https?:\/\/[^\s]+/gi, '[URL]')
                     .replace(/\b(?:\d{1,3}\.){3}\d{1,3}\b/g, '[IP]')
                     .substring(0, 200); // Limit length
    }
    
    createArticleHtml({ article, className, sentimentBadge, contentLength, showInfluence }) {
        if (!article) return '';
        
        const title = this.escapeHtml(article.title || 'Untitled');
        const source = this.escapeHtml(article.source || 'Unknown');
        const url = this.escapeHtml(article.url || '');
        const imageUrl = this.escapeHtml(article.image_url || '');
        
        const imageHtml = imageUrl ?
            `<img src="${imageUrl}" alt="${title}" class="featured-article-image" onerror="this.style.display='none'" loading="lazy">` :
            `<div class="featured-article-image"></div>`;
            
        const contentHtml = article.content ?
            `<p class="featured-article-summary">${this.escapeHtml(article.content.substring(0, contentLength))}...</p>` :
            '';
            
        const influenceHtml = showInfluence ? this.renderInfluenceTagging(article, className !== 'main-featured-article') : '';
        
        return `
            <div class="${className} featured-article" onclick="this.openArticle('${url}')" role="button" tabindex="0" onkeypress="if(event.key==='Enter') this.click()">
                ${imageHtml}
                <div class="featured-article-content">
                    <h6 class="featured-article-title">${title}</h6>
                    <div class="featured-article-meta">
                        <span class="featured-article-source">${source}</span>
                        <span class="news-list-date">${this.formatDateWithTime(article.date || article.publish_time)}</span>
                        ${sentimentBadge}
                    </div>
                    ${contentHtml}
                    ${influenceHtml}
                </div>
            </div>
        `;
    }
    
    openArticle(url) {
        if (url && typeof url === 'string') {
            try {
                // Validate URL before opening
                const urlObj = new URL(url);
                if (urlObj.protocol === 'http:' || urlObj.protocol === 'https:') {
                    window.open(url, '_blank', 'noopener,noreferrer');
                }
            } catch (error) {
                console.error('Invalid URL:', error);
            }
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        window.news = new News();
    } catch (error) {
        console.error('Failed to initialize News:', error);
    }
});

// Handle page unload to cleanup
window.addEventListener('beforeunload', function() {
    if (window.news?.abortController) {
        window.news.abortController.abort();
    }
});
