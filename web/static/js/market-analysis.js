/**
 * Market Analysis JavaScript Module
 * Handles interactive charts and AI analysis display
 */

class MarketAnalysis {
    constructor() {
        this.currentTicker = 'SPY';
        this.currentApiProvider = 'gemini';
        this.chartData = null;
        this.analysisData = null;
        this.currentOutlookData = null;
        this.currentOutlookId = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Ticker selection
        const tickerSelect = document.getElementById('ticker-select');
        if (tickerSelect) {
            tickerSelect.addEventListener('change', (e) => {
                this.currentTicker = e.target.value;
                this.loadChartData();
            });
        }

        // Load analysis button
        const loadAnalysisBtn = document.getElementById('load-analysis-btn');
        if (loadAnalysisBtn) {
            loadAnalysisBtn.addEventListener('click', () => {
                this.loadAnalysis();
            });
        }

        // API provider selection
        const apiRadios = document.querySelectorAll('input[name="api-options"]');
        apiRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.currentApiProvider = e.target.value;
                this.loadAnalysis();
            });
        });
    }

    async loadInitialData() {
        await this.loadChartData();
        await this.loadAnalysis();
    }

    async loadChartData() {
        try {
            this.showChartLoading();

            // Get date range (last 6 months)
            const endDate = new Date();
            const startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 6);

            const params = new URLSearchParams({
                start_date: startDate.toISOString().split('T')[0],
                end_date: endDate.toISOString().split('T')[0]
            });

            // Use SP500 API for all tickers for now
            const response = await fetch(`/api/sp500?${params.toString()}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to load chart data');
            }

            this.chartData = data;
            this.renderChart(data);

        } catch (error) {
            console.error('Error loading chart data:', error);
            this.showChartError(error.message);
        }
    }

    async loadAnalysis() {
        try {
            this.showAnalysisLoading();
            
            const response = await fetch(`/api/llm-prediction?api=${this.currentApiProvider}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to load AI analysis');
            }

            this.analysisData = data;
            this.renderAnalysis(data);

        } catch (error) {
            console.error('Error loading AI analysis:', error);
            this.showAnalysisError(error.message);
        }
    }

    showChartLoading() {
        const chartContainer = document.getElementById('price-chart');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading chart...</span>
                    </div>
                    <p class="mt-2">Loading ${this.currentTicker} price chart...</p>
                </div>
            `;
        }
    }

    showChartError(message) {
        const chartContainer = document.getElementById('price-chart');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="error-message">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Error loading chart:</strong> ${this.escapeHtml(message)}
                </div>
            `;
        }
    }

    showAnalysisLoading() {
        const analysisContainer = document.getElementById('ai-analysis-content');
        if (analysisContainer) {
            analysisContainer.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">Loading AI analysis...</span>
                    </div>
                    <p class="mt-2">Generating AI market analysis with ${this.currentApiProvider}...</p>
                </div>
            `;
        }
    }

    showAnalysisError(message) {
        const analysisContainer = document.getElementById('ai-analysis-content');
        if (analysisContainer) {
            analysisContainer.innerHTML = `
                <div class="error-message">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Error loading AI analysis:</strong> ${this.escapeHtml(message)}
                </div>
            `;
        }
    }

    renderChart(data) {
        const chartContainer = document.getElementById('price-chart');
        if (!chartContainer) {
            this.showChartError('Chart container not found');
            return;
        }

        let dates, prices, volumes;
        
        // Handle different data formats
        if (data.prices && Array.isArray(data.prices)) {
            dates = data.prices.map(item => item.date);
            prices = data.prices.map(item => item.close);
            volumes = data.prices.map(item => item.volume);
        } else if (data.dates && data.close && data.volume) {
            dates = data.dates;
            prices = data.close;
            volumes = data.volume;
        } else if (Array.isArray(data)) {
            dates = data.map(item => item.date);
            prices = data.map(item => item.close);
            volumes = data.map(item => item.volume);
        } else {
            console.error('Invalid chart data structure:', data);
            this.showChartError('Invalid chart data format');
            return;
        }
        
        if (!dates || !prices || dates.length === 0 || prices.length === 0) {
            console.error('Empty or invalid chart data arrays');
            this.showChartError('No chart data available');
            return;
        }

        const traces = this.createChartTraces(dates, prices, volumes);
        const layout = this.createChartLayout();
        const config = this.createChartConfig();

        Plotly.newPlot(chartContainer, traces, layout, config);
    }

    createChartTraces(dates, prices, volumes) {
        return [
            {
                x: dates,
                y: prices,
                type: 'scatter',
                mode: 'lines',
                name: `${this.currentTicker} Price`,
                line: { color: '#007bff', width: 2 },
                yaxis: 'y1',
                hovertemplate: '<b>%{fullData.name}</b><br>Date: %{x}<br>Price: $%{y:.2f}<br><extra></extra>'
            },
            {
                x: dates,
                y: volumes,
                type: 'bar',
                name: 'Volume',
                marker: { color: 'rgba(255,165,0,0.6)' },
                yaxis: 'y2',
                hovertemplate: '<b>Volume</b><br>Date: %{x}<br>Volume: %{y:,}<br><extra></extra>'
            }
        ];
    }

    createChartLayout() {
        return {
            title: { text: `${this.currentTicker} Price Chart`, font: { size: 16 } },
            xaxis: { title: 'Date', type: 'date' },
            yaxis: { title: 'Price ($)', tickformat: '$.2f', side: 'left' },
            yaxis2: { title: 'Volume', overlaying: 'y', side: 'right', showgrid: false },
            hovermode: 'x unified',
            showlegend: true,
            margin: { t: 50, r: 50, b: 50, l: 60 },
            plot_bgcolor: 'rgba(0,0,0,0)',
            paper_bgcolor: 'rgba(0,0,0,0)'
        };
    }

    createChartConfig() {
        return {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false
        };
    }

    renderAnalysis(data) {
        const analysisContainer = document.getElementById('ai-analysis-content');
        if (!analysisContainer) return;

        let html = '';

        // Market Overview Section - Group numeric data together
        let hasNumericData = (data.prediction && data.confidence) || data.probabilities || (data.critical_levels && Object.keys(data.critical_levels).length > 0);
        
        if (hasNumericData) {
            html += `
                <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); border: 1px solid #93c5fd; padding: 24px; margin: 24px 0; border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h6 style="color: #1e40af; font-weight: 700; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 10px;">
                        <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 8px; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-graph-up-arrow" style="font-size: 16px;"></i>
                        </div>
                        Market Overview
                    </h6>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">
            `;
            
            // Market Direction Prediction
            if (data.prediction && data.confidence) {
                const confidencePercent = (data.confidence * 100).toFixed(1);
                const predictionClass = data.prediction === 'up' ? '#10b981' : 
                                      data.prediction === 'down' ? '#ef4444' : '#6b7280';
                const predictionIcon = data.prediction === 'up' ? 'up' : data.prediction === 'down' ? 'down' : 'right';
                
                html += `
                    <div style="background: rgba(255, 255, 255, 0.9); border-radius: 12px; padding: 20px; border: 1px solid #bfdbfe;">
                        <h6 style="color: #1e40af; font-weight: 600; margin-bottom: 15px; font-size: 14px;">MARKET PREDICTION</h6>
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                            <i class="bi bi-arrow-${predictionIcon}" style="color: ${predictionClass}; font-size: 28px;"></i>
                            <div>
                                <div style="color: ${predictionClass}; font-weight: 700; font-size: 24px; line-height: 1;">
                                    ${data.prediction.toUpperCase()}
                                </div>
                                <div style="color: #64748b; font-size: 12px; font-weight: 500;">
                                    ${confidencePercent}% Confidence
                                </div>
                            </div>
                        </div>
                        <div style="background: #f1f5f9; border-radius: 8px; height: 8px; overflow: hidden;">
                            <div style="background: ${predictionClass}; height: 100%; width: ${confidencePercent}%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                `;
            }
            
            // Critical Levels
            if (data.critical_levels && Object.keys(data.critical_levels).length > 0) {
                html += `
                    <div style="background: rgba(255, 255, 255, 0.9); border-radius: 12px; padding: 20px; border: 1px solid #bfdbfe;">
                        <h6 style="color: #1e40af; font-weight: 600; margin-bottom: 15px; font-size: 14px;">CRITICAL LEVELS</h6>
                        <div style="display: grid; gap: 12px;">
                `;
                
                Object.entries(data.critical_levels).forEach(([key, value]) => {
                    if (typeof value === 'number') {
                        html += `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #f1f5f9;">
                                <span style="color: #475569; font-weight: 500; font-size: 13px;">${this.capitalizeFirst(key)}</span>
                                <span style="color: #1e293b; font-weight: 700; font-size: 14px;">$${value.toFixed(2)}</span>
                            </div>
                        `;
                    } else if (typeof value === 'string') {
                        html += `
                            <div style="padding: 8px 0; border-bottom: 1px solid #f1f5f9;">
                                <div style="color: #475569; font-weight: 500; font-size: 13px; margin-bottom: 4px;">${this.capitalizeFirst(key)}</div>
                                <div style="color: #1e293b; font-weight: 600; font-size: 13px;">${this.escapeHtml(value)}</div>
                            </div>
                        `;
                    }
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
            
            html += `
                    </div>
            `;
            
            // Probabilities
            if (data.probabilities) {
                html += `
                    <div style="margin-bottom: 20px;">
                        <h6 style="color: #1e40af; font-weight: 600; margin-bottom: 15px; font-size: 14px;">PROBABILITY BREAKDOWN</h6>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px;">
                `;
                
                Object.entries(data.probabilities).forEach(([label, prob]) => {
                    const percentage = (prob * 100).toFixed(1);
                    const colorClass = label === 'positive' ? '#10b981' : 
                                     label === 'negative' ? '#ef4444' : '#f59e0b';
                    
                    html += `
                        <div style="background: rgba(255, 255, 255, 0.9); border-radius: 10px; padding: 16px; text-align: center; border: 1px solid #bfdbfe;">
                            <div style="color: ${colorClass}; font-weight: 700; font-size: 11px; text-transform: uppercase; margin-bottom: 8px; letter-spacing: 0.5px;">
                                ${label}
                            </div>
                            <div style="color: #1e293b; font-weight: 800; font-size: 20px;">
                                ${percentage}%
                            </div>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
            
            html += `
                </div>
            `;
        }

        // Key Evidence - Modern UI Design
        if (data.key_evidence && data.key_evidence.length > 0) {            
            html += `
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border: 1px solid #cbd5e1; padding: 24px; margin: 24px 0; border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h6 style="color: #1e293b; font-weight: 700; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 8px;">
                        <i class="bi bi-lightbulb-fill" style="color: #3b82f6;"></i>
                        Key Market Evidence
                        <span style="background: #3b82f6; color: white; font-size: 12px; padding: 2px 8px; border-radius: 12px; font-weight: 600;">${data.key_evidence.length}</span>
                    </h6>
                    <div style="display: grid; gap: 12px;">
            `;

            data.key_evidence.forEach((evidence, index) => {
                let evidenceText = evidence.fact || 'No evidence text available';
                
                // Clean up the evidence text by removing [Article X, Y, Z] references
                evidenceText = evidenceText.replace(/\s*\[Article\s+[\d,\s]+\]\s*\.?\s*$/i, '');
                
                // Check if we have a URL to make it clickable
                const hasUrl = evidence.url && evidence.url !== 'N/A';
                const isMarketData = evidence.article_ref === "MARKET" || evidence.source === "N/A";
                
                html += `
                    <div style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 16px; transition: all 0.2s ease; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; width: 4px; height: 100%; background: ${isMarketData ? '#10b981' : '#3b82f6'};"></div>
                        <div style="margin-left: 12px;">
                `;
                
                if (hasUrl && !isMarketData) {
                    // Make the entire evidence text clickable
                    html += `
                        <a href="${this.escapeHtml(evidence.url)}" target="_blank" rel="noopener noreferrer" style="text-decoration: none; color: inherit; display: block;">
                            <div style="color: #1e293b; font-weight: 500; font-size: 15px; line-height: 1.6; margin: 0; padding: 0; cursor: pointer; transition: color 0.2s ease;" onmouseover="this.style.color='#3b82f6'" onmouseout="this.style.color='#1e293b'">
                                ${this.escapeHtml(evidenceText)}
                            </div>
                            <div style="margin-top: 8px; display: flex; align-items: center; gap: 6px; font-size: 12px; color: #64748b;">
                                <i class="bi bi-box-arrow-up-right" style="font-size: 10px;"></i>
                                <span>Click to read full article</span>
                            </div>
                        </a>
                    `;
                } else {
                    // Non-clickable for market data
                    html += `
                        <div style="color: #1e293b; font-weight: 500; font-size: 15px; line-height: 1.6; margin: 0; padding: 0;">
                            ${this.escapeHtml(evidenceText)}
                        </div>
                        <div style="margin-top: 8px; display: flex; align-items: center; gap: 6px; font-size: 12px; color: #64748b;">
                            <i class="bi bi-graph-up" style="font-size: 10px;"></i>
                            <span>Market data analysis</span>
                        </div>
                    `;
                }
                
                html += `
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        // Dominant Market Theme - Harmonious Design
        if (data.dominant_theme && data.dominant_theme.theme) {
            html += `
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%); border: 1px solid #cbd5e1; padding: 24px; margin: 24px 0; border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); position: relative; overflow: hidden;">
                    <div style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1)); border-radius: 50%; opacity: 0.6;"></div>
                    <div style="position: absolute; bottom: -30px; left: -30px; width: 60px; height: 60px; background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(167, 243, 208, 0.1)); border-radius: 50%; opacity: 0.4;"></div>
                    
                    <h6 style="color: #475569; font-weight: 700; margin-bottom: 16px; font-size: 18px; display: flex; align-items: center; gap: 10px; position: relative; z-index: 1;">
                        <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 8px; border-radius: 10px; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);">
                            <i class="bi bi-lightbulb" style="font-size: 16px;"></i>
                        </div>
                        Dominant Market Theme
                    </h6>
                    
                    <div style="background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 20px; margin-bottom: 16px; position: relative; z-index: 1; border-left: 4px solid #3b82f6; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                        <p style="color: #334155; font-weight: 500; line-height: 1.7; margin: 0; font-size: 16px;">
                            ${this.escapeHtml(data.dominant_theme.theme)}
                        </p>
                    </div>
            `;

            // Add supporting articles if available
            if (data.dominant_theme.supporting_articles && data.dominant_theme.supporting_articles.length > 0) {
                html += `
                    <div style="position: relative; z-index: 1;">
                        <h6 style="color: #475569; font-weight: 600; font-size: 14px; margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                            <i class="bi bi-collection" style="font-size: 14px;"></i>
                            Supporting Articles
                            <span style="background: rgba(59, 130, 246, 0.1); color: #3b82f6; font-size: 11px; padding: 2px 6px; border-radius: 8px; font-weight: 700;">${data.dominant_theme.supporting_articles.length}</span>
                        </h6>
                        <div style="display: grid; gap: 8px;">
                `;

                data.dominant_theme.supporting_articles.forEach(article => {
                    const hasUrl = article.url && article.url !== 'N/A';
                    const articleTitle = article.article_title || 'Article';
                    const articleSource = article.source || 'Unknown Source';
                    
                    html += `
                        <div style="background: rgba(255, 255, 255, 0.9); border-radius: 8px; padding: 12px; border: 1px solid rgba(203, 213, 225, 0.5); transition: all 0.2s ease; hover: box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                    `;
                    
                    if (hasUrl) {
                        html += `
                            <a href="${this.escapeHtml(article.url)}" target="_blank" rel="noopener noreferrer" style="text-decoration: none; color: inherit; display: block;">
                                <div style="display: flex; align-items: start; gap: 10px;">
                                    <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 6px; border-radius: 6px; flex-shrink: 0; margin-top: 2px;">
                                        <i class="bi bi-newspaper" style="font-size: 12px;"></i>
                                    </div>
                                    <div style="flex: 1; min-width: 0;">
                                        <div style="color: #334155; font-weight: 600; font-size: 13px; line-height: 1.4; margin-bottom: 4px; cursor: pointer; transition: color 0.2s ease;" onmouseover="this.style.color='#3b82f6'" onmouseout="this.style.color='#334155'">
                                            ${this.escapeHtml(articleTitle)}
                                        </div>
                                        <div style="color: #64748b; font-size: 11px; font-weight: 500; display: flex; align-items: center; gap: 4px;">
                                            <span>${this.escapeHtml(articleSource)}</span>
                                            <i class="bi bi-box-arrow-up-right" style="font-size: 9px;"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        `;
                    } else {
                        html += `
                            <div style="display: flex; align-items: start; gap: 10px;">
                                <div style="background: #64748b; color: white; padding: 6px; border-radius: 6px; flex-shrink: 0; margin-top: 2px;">
                                    <i class="bi bi-newspaper" style="font-size: 12px;"></i>
                                </div>
                                <div style="flex: 1; min-width: 0;">
                                    <div style="color: #334155; font-weight: 600; font-size: 13px; line-height: 1.4; margin-bottom: 4px;">
                                        ${this.escapeHtml(articleTitle)}
                                    </div>
                                    <div style="color: #64748b; font-size: 11px; font-weight: 500;">
                                        ${this.escapeHtml(articleSource)}
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                    
                    html += `</div>`;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            html += `</div>`;
        }

        // Interactive Detailed Outlook Section
        if (data.detailed_outlook && Object.keys(data.detailed_outlook).length > 0) {
            // Sort terms to show short_term first, then mid_term, then long_term
            const termOrder = ['short_term', 'mid_term', 'long_term'];
            const outlookTerms = Object.keys(data.detailed_outlook).sort((a, b) => {
                const aIndex = termOrder.indexOf(a);
                const bIndex = termOrder.indexOf(b);
                if (aIndex === -1 && bIndex === -1) return a.localeCompare(b);
                if (aIndex === -1) return 1;
                if (bIndex === -1) return -1;
                return aIndex - bIndex;
            });
            const randomId = Math.random().toString(36).substring(2, 9);
            
            // Store the outlook data for tab switching
            this.currentOutlookData = data.detailed_outlook;
            this.currentOutlookId = randomId;
            
            html += `
                <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border: 1px solid #86efac; padding: 24px; margin: 24px 0; border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h6 style="color: #166534; font-weight: 700; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 10px;">
                        <div style="background: #166534; color: #f0fdf4; padding: 8px; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-clock-history" style="font-size: 16px;"></i>
                        </div>
                        Market Outlook
                    </h6>
                    
                    <!-- Tab Navigation -->
                    <div style="display: flex; gap: 4px; margin-bottom: 20px; background: rgba(255, 255, 255, 0.7); padding: 4px; border-radius: 12px; border: 1px solid #d1fae5;">
            `;
            
            outlookTerms.forEach((term, index) => {
                const isActive = index === 0; // Default to first tab (usually short_term)
                const termDisplay = term.replace('_', ' ').split(' ').map(word => 
                    word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                
                html += `
                    <button onclick="window.marketAnalysis.switchOutlookTab('${term}')" 
                            id="tab_${term}_${randomId}"
                            style="flex: 1; padding: 10px 16px; border: none; border-radius: 8px; font-weight: 600; font-size: 14px; cursor: pointer; transition: all 0.2s ease; background: ${isActive ? '#166534' : 'transparent'}; color: ${isActive ? 'white' : '#166534'};">
                        ${termDisplay}
                    </button>
                `;
            });
            
            html += `
                    </div>
                    
                    <!-- Tab Content -->
                    <div id="outlook_content_${randomId}">
            `;
            
            // Generate content for each tab
            outlookTerms.forEach((term, index) => {
                const outlook = data.detailed_outlook[term];
                if (!outlook) return;
                
                const direction = outlook.direction || 'FLAT';
                const expectedMove = outlook.expected_move_pct || 'Unknown';
                const confidence = outlook.confidence || 'LOW';
                const keyEvidence = outlook.key_evidence || [];
                const isActive = index === 0;
                
                html += `
                    <div id="content_${term}_${randomId}" style="display: ${isActive ? 'block' : 'none'};">
                        <div style="background: rgba(255, 255, 255, 0.9); border-radius: 12px; padding: 20px; border: 1px solid #d1fae5;">
                            <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 24px; margin-bottom: 20px;">
                                <!-- Metrics -->
                                <div style="display: flex; flex-direction: column; gap: 16px;">
                                    <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 12px;">
                                        <div style="font-weight: 600; color: #166534; font-size: 12px; margin-bottom: 6px;">DIRECTION</div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <i class="bi bi-arrow-${direction === 'UP' ? 'up' : direction === 'DOWN' ? 'down' : 'right'}" style="color: ${direction === 'UP' ? '#10b981' : direction === 'DOWN' ? '#ef4444' : '#6b7280'}; font-size: 18px;"></i>
                                            <span style="font-weight: 700; font-size: 16px; color: ${direction === 'UP' ? '#10b981' : direction === 'DOWN' ? '#ef4444' : '#6b7280'};">${direction}</span>
                                        </div>
                                    </div>
                                    
                                    <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 12px;">
                                        <div style="font-weight: 600; color: #166534; font-size: 12px; margin-bottom: 6px;">EXPECTED MOVE</div>
                                        <div style="font-weight: 700; font-size: 16px; color: #1e293b;">${this.escapeHtml(expectedMove)}</div>
                                    </div>
                                    
                                    <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 12px;">
                                        <div style="font-weight: 600; color: #166534; font-size: 12px; margin-bottom: 6px;">CONFIDENCE</div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <div style="background: ${confidence === 'HIGH' ? '#10b981' : confidence === 'MEDIUM' ? '#f59e0b' : '#6b7280'}; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 600;">
                                                ${confidence}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Evidence -->
                                <div>
                                    <h6 style="color: #166534; font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                                        <i class="bi bi-clipboard-data" style="font-size: 14px;"></i>
                                        Supporting Evidence
                                        <span style="background: rgba(22, 101, 52, 0.1); color: #166534; font-size: 11px; padding: 2px 6px; border-radius: 8px; font-weight: 700;">${keyEvidence.length}</span>
                                    </h6>
                                    <div style="display: flex; flex-direction: column; gap: 8px; max-height: 200px; overflow-y: auto;">
                `;
                
                keyEvidence.forEach((evidence) => {
                    let evidenceText = evidence.fact || 'No evidence text available';
                    evidenceText = evidenceText.replace(/\s*\[Article\s+[\d,\s]+\]\s*\.?\s*$/i, '');
                    
                    const hasUrl = evidence.url && evidence.url !== 'N/A';
                    const isMarketData = evidence.article_ref === "MARKET" || evidence.source === "N/A";
                    
                    html += `
                        <div style="background: #ffffff; border: 1px solid #d1fae5; border-radius: 8px; padding: 12px; position: relative; overflow: hidden; transition: all 0.2s ease;">
                            <div style="position: absolute; top: 0; left: 0; width: 3px; height: 100%; background: ${isMarketData ? '#10b981' : '#059669'};"></div>
                            <div style="margin-left: 8px;">
                    `;
                    
                    if (hasUrl && !isMarketData) {
                        html += `
                            <a href="${this.escapeHtml(evidence.url)}" target="_blank" rel="noopener noreferrer" style="text-decoration: none; color: inherit; display: block;">
                                <div style="color: #1e293b; font-weight: 500; font-size: 13px; line-height: 1.5; margin: 0; cursor: pointer; transition: color 0.2s ease;" onmouseover="this.style.color='#059669'" onmouseout="this.style.color='#1e293b'">
                                    ${this.escapeHtml(evidenceText)}
                                </div>
                                <div style="margin-top: 4px; font-size: 10px; color: #6b7280; display: flex; align-items: center; gap: 4px;">
                                    <i class="bi bi-box-arrow-up-right" style="font-size: 8px;"></i>
                                    <span>Read article</span>
                                </div>
                            </a>
                        `;
                    } else {
                        html += `
                            <div style="color: #1e293b; font-weight: 500; font-size: 13px; line-height: 1.5; margin: 0;">
                                ${this.escapeHtml(evidenceText)}
                            </div>
                            <div style="margin-top: 4px; font-size: 10px; color: #6b7280; display: flex; align-items: center; gap: 4px;">
                                <i class="bi bi-graph-up" style="font-size: 8px;"></i>
                                <span>Market analysis</span>
                            </div>
                        `;
                    }
                    
                    html += `
                            </div>
                        </div>
                    `;
                });
                
                html += `
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += `
                    </div>
                </div>
            `;
        }

        // Metadata
        if (data.metadata) {
            html += `
                <div class="mt-3 pt-3 border-top">
                    <small class="text-muted">
                        <i class="bi bi-info-circle"></i>
                        Analysis generated using ${this.escapeHtml(data.metadata.api || 'AI')}
                        ${data.metadata.model ? `(${this.escapeHtml(data.metadata.model)})` : ''}
                        at ${new Date(data.metadata.timestamp).toLocaleString()}
                    </small>
                </div>
            `;
        }
        
        analysisContainer.innerHTML = html;
    }
    
    switchOutlookTab(term) {
        if (!this.currentOutlookData || !this.currentOutlookId) {
            console.error('No outlook data available for tab switching');
            return;
        }
        
        const randomId = this.currentOutlookId;
        const outlookTerms = Object.keys(this.currentOutlookData);
        
        // Hide all content
        outlookTerms.forEach(t => {
            const contentEl = document.getElementById(`content_${t}_${randomId}`);
            if (contentEl) contentEl.style.display = 'none';
        });
        
        // Remove active state from all tabs
        outlookTerms.forEach(t => {
            const tabEl = document.getElementById(`tab_${t}_${randomId}`);
            if (tabEl) {
                tabEl.style.background = 'transparent';
                tabEl.style.color = '#166534';
            }
        });
        
        // Show selected content and activate tab
        const selectedContent = document.getElementById(`content_${term}_${randomId}`);
        const selectedTab = document.getElementById(`tab_${term}_${randomId}`);
        
        if (selectedContent) selectedContent.style.display = 'block';
        if (selectedTab) {
            selectedTab.style.background = '#166534';
            selectedTab.style.color = 'white';
        }
    }

    // Utility Methods
    escapeHtml(text) {
        if (typeof text !== 'string') return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1).replace('_', ' ');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.marketAnalysis = new MarketAnalysis();
});