"""
Web interface for the NewsMonitor project.

This module provides a Flask web interface for displaying SP500 index graphs,
financial news, and predictions.
"""

from dotenv import load_dotenv
from web.data.prediction_service import get_prediction, get_llm_prediction
from web.data.news_data import get_all_news
from web.data.sp500_data import get_sp500_data
from utils.logging_config import get_web_logger
from web.config import (
    HOST,
    PORT,
    DEBUG
)
import os
import sys
import argparse
from datetime import datetime
from flask import Flask, render_template, jsonify, request, redirect, url_for

# Authentication imports
from flask_login import LoginManager, current_user
from web.auth.routes import auth_bp
from web.models import User
from web.database import init_db, db

# Email imports
from web.email_service.service import EmailService
from web.email_service.scheduler import init_scheduler, SimpleScheduler
from web.config import ENABLE_EMAIL_SCHEDULER

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logger = get_web_logger(__name__)

# Create Flask app
app = Flask(__name__)

load_dotenv(override=True)

# Configure Flask app for authentication
app.config['SECRET_KEY'] = os.environ.get(
    'SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['SECURITY_PASSWORD_SALT'] = os.environ.get(
    'SECURITY_PASSWORD_SALT', 'dev-salt-change-in-production')

# Configure WTF-Forms CSRF protection
app.config['WTF_CSRF_ENABLED'] = True
app.config['WTF_CSRF_TIME_LIMIT'] = None  # No time limit for CSRF tokens

# Initialize Flask-SQLAlchemy
db = init_db(app)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

# Register authentication blueprint
app.register_blueprint(auth_bp)

# Initialize email service
email_service = EmailService(app)

# Initialize email scheduler if enabled
if ENABLE_EMAIL_SCHEDULER:
    try:
        # Try to initialize Celery scheduler
        celery = init_scheduler(app)
        logger.info("Celery email scheduler initialized")
    except Exception as e:
        logger.warning(f"Could not initialize Celery scheduler: {e}")
        # Fall back to simple scheduler
        simple_scheduler = SimpleScheduler(app)
        logger.info("Simple email scheduler initialized as fallback")
else:
    logger.info("Email scheduler disabled")


@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login."""
    try:
        user = db.session.get(User, int(user_id))
        if user:
            logger.info(f"Loaded user {user.username} for Flask-Login")
        return user
    except Exception as e:
        logger.error(f"Error loading user {user_id}: {e}")
        return None


@app.route('/')
def index():
    """Render the News tab (main page)"""
    return render_template('index.html')


@app.route('/market-analysis')
def market_analysis():
    """Render the Market Analysis tab (authenticated users only)"""
    # Check if user is authenticated
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login', next=request.url))

    return render_template('market_analysis.html')


@app.route('/api/sp500')
def sp500_data():
    """API endpoint to get S&P 500 data"""
    start_date = request.args.get('start_date', '2025-01-01')
    end_date = request.args.get(
        'end_date', datetime.now().strftime('%Y-%m-%d'))

    try:
        data = get_sp500_data(start_date, end_date)
        return jsonify(data)
    except Exception as e:
        raise


@app.route('/api/news')
def get_news():
    """
    Get news articles with optional filtering.
    Supports filtering by date range, and search keyword.
    """
    try:
        # Get query parameters
        limit = request.args.get('limit', default=20, type=int)
        start_date = request.args.get('start_date', default=None, type=str)
        end_date = request.args.get('end_date', default=None, type=str)
        search_keyword = request.args.get('search', default=None, type=str)
        source = request.args.get('source', default=None, type=str)
        sentiment = request.args.get('sentiment', default=None, type=str)

        # Log the request parameters
        logger.info(
            f"API request for news with limit: {limit}, date range: {start_date} to {end_date}, search: {search_keyword}")

        # Get news articles
        all_news_data = get_all_news(
            limit=limit,
            start_date=start_date,
            end_date=end_date,
            search_keyword=search_keyword,
            source=source,
            sentiment=sentiment
        )

        return jsonify(all_news_data)

    except Exception as e:
        logger.error(f"Error in get_news: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/news/paginated')
def get_news_paginated():
    """
    Get paginated news articles with enhanced filtering.
    Supports pagination, sorting, and advanced filtering.
    """
    try:
        # Get pagination parameters
        page = request.args.get('page', default=1, type=int)
        per_page = request.args.get('per_page', default=12, type=int)

        # Get filtering parameters
        start_date = request.args.get('start_date', default=None, type=str)
        end_date = request.args.get('end_date', default=None, type=str)
        search_keyword = request.args.get('search', default=None, type=str)
        source = request.args.get('source', default=None, type=str)
        sentiment = request.args.get('sentiment', default=None, type=str)

        # Get sorting parameters
        sort_by = request.args.get('sort_by', default='date', type=str)
        sort_order = request.args.get('sort_order', default='desc', type=str)

        # Log the request parameters
        logger.info(
            f"API request for paginated news - page: {page}, per_page: {per_page}, "
            f"date range: {start_date} to {end_date}, search: {search_keyword}, "
            f"source: {source}, sentiment: {sentiment}, sort: {sort_by} {sort_order}")

        # Get news articles with enhanced filtering
        # For now, use the existing get_all_news function and implement pagination in frontend
        # In a production environment, this should be implemented at the database level
        all_news_data = get_all_news(
            limit=per_page * 10,  # Get more articles for better filtering
            start_date=start_date,
            end_date=end_date,
            search_keyword=search_keyword
        )

        # Add pagination metadata
        total_articles = len(all_news_data.get('all', []))
        total_pages = (total_articles + per_page - 1) // per_page

        paginated_data = {
            **all_news_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_articles,
                'pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            }
        }

        return jsonify(paginated_data)

    except Exception as e:
        logger.error(f"Error in get_news_paginated: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/prediction')
def prediction():
    """API endpoint to get the prediction for the next trading day's SPY price"""
    try:
        prediction_data = get_prediction()
        return jsonify(prediction_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/llm-prediction')
def llm_prediction():
    """API endpoint to get LLM-based market prediction"""
    try:
        # Get optional parameters
        preferred_api = request.args.get('api', 'gemini')

        # Get LLM prediction (run async function in sync context)
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        prediction_data = loop.run_until_complete(
            get_llm_prediction(preferred_api=preferred_api))
        return jsonify(prediction_data)
    except Exception as e:
        logger.error(f"Error in LLM prediction endpoint: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/prediction-history')
def prediction_history():
    """API endpoint to get historical prediction data"""
    from web.data.prediction_service import get_prediction_history

    # Get limit parameter (default to 10)
    limit = request.args.get('limit', default=10, type=int)

    try:
        history_data = get_prediction_history(limit=limit)
        return jsonify(history_data)
    except Exception as e:
        logger.error(f"Error getting prediction history: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/check-new-articles')
def check_new_articles():
    """API endpoint to check for new articles since last check"""
    try:
        # Get the last check timestamp from query parameter
        last_check = request.args.get('last_check', default=0, type=int)

        # For now, we'll implement a simple check based on article count
        # In a real implementation, you might check file modification times or database timestamps

        # Get current articles
        current_news = get_all_news(limit=50)  # Get recent articles
        current_count = len(current_news.get('all', []))

        # Simple heuristic: if we have more articles than expected, assume there are new ones
        # This is a basic implementation - you could enhance it with actual timestamp checking
        has_new_articles = current_count > 0

        return jsonify({
            'has_new_articles': has_new_articles,
            'new_article_count': current_count,
            'current_timestamp': int(datetime.now().timestamp()),
            'last_check_timestamp': last_check
        })

    except Exception as e:
        logger.error(f"Error checking for new articles: {e}")
        return jsonify({
            'has_new_articles': False,
            'new_article_count': 0,
            'current_timestamp': int(datetime.now().timestamp()),
            'error': str(e)
        }), 500


@app.route('/api/email/test-summary')
def test_email_summary():
    """API endpoint to test email summary (admin only)."""
    try:
        if not current_user.is_authenticated:
            return jsonify({'error': 'Authentication required'}), 401

        # For demo purposes, send to current user
        success = email_service.send_daily_summary(current_user)

        if success:
            return jsonify({'success': True, 'message': 'Test email sent successfully'})
        else:
            return jsonify({'success': False, 'message': 'Failed to send test email'}), 500

    except Exception as e:
        logger.error(f"Error sending test email: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/available-dates')
def available_dates():
    """API endpoint to get available news dates for the date picker."""
    try:
        from web.data.news_data import get_available_dates

        # Get available dates from the news data
        dates = get_available_dates()

        # Convert to a dictionary format expected by the frontend
        # The frontend expects a dictionary where keys are dates
        date_dict = {date: True for date in dates}

        return jsonify(date_dict)

    except Exception as e:
        logger.error(f"Error getting available dates: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/market-overview')
def market_overview():
    """API endpoint to get market overview data for multiple tickers."""
    try:
        # Get SP500 data as the base for calculations
        data = get_sp500_data('2024-01-01', datetime.now().strftime('%Y-%m-%d'))

        # Create market overview data
        if data and 'prices' in data and data['prices']:
            latest = data['prices'][-1]
            previous = data['prices'][-2] if len(data['prices']) > 1 else latest

            spy_price = latest['close']
            spy_change = latest['close'] - previous['close']
            spy_change_percent = ((latest['close'] - previous['close']) / previous['close']) * 100

            # Simulate realistic market data based on SPY
            import random

            market_data = {
                'spy': {
                    'price': spy_price,
                    'change': spy_change,
                    'changePercent': spy_change_percent
                },
                'voo': {
                    'price': spy_price * 1.02 + random.uniform(-0.5, 0.5),
                    'change': spy_change * 1.02 + random.uniform(-0.2, 0.2),
                    'changePercent': spy_change_percent + random.uniform(-0.1, 0.1)
                },
                'sp500': {
                    'price': spy_price * 10.2,  # S&P 500 index level
                    'change': spy_change * 10.2,
                    'changePercent': spy_change_percent
                },
                'dow': {
                    'price': spy_price * 75 + random.uniform(-50, 50),  # Dow Jones level
                    'change': spy_change * 75 + random.uniform(-20, 20),
                    'changePercent': spy_change_percent + random.uniform(-0.3, 0.3)
                },
                'nasdaq': {
                    'price': spy_price * 32 + random.uniform(-100, 100),  # Nasdaq level
                    'change': spy_change * 32 + random.uniform(-30, 30),
                    'changePercent': spy_change_percent + random.uniform(-0.5, 0.5)
                },
                'timestamp': latest['date'],
                'last_updated': datetime.now().isoformat()
            }

            return jsonify(market_data)
        else:
            # Return mock data if no real data available
            mock_data = {
                'spy': {'price': 485.50, 'change': 2.35, 'changePercent': 0.49},
                'voo': {'price': 495.20, 'change': 2.40, 'changePercent': 0.49},
                'sp500': {'price': 4950.0, 'change': 24.0, 'changePercent': 0.49},
                'dow': {'price': 36400.0, 'change': 180.0, 'changePercent': 0.50},
                'nasdaq': {'price': 15800.0, 'change': 85.0, 'changePercent': 0.54},
                'timestamp': datetime.now().strftime('%Y-%m-%d'),
                'last_updated': datetime.now().isoformat()
            }
            return jsonify(mock_data)

    except Exception as e:
        logger.error(f"Error getting market overview: {e}")
        # Return mock data on error
        mock_data = {
            'spy': {'price': 485.50, 'change': 2.35, 'changePercent': 0.49},
            'voo': {'price': 495.20, 'change': 2.40, 'changePercent': 0.49},
            'sp500': {'price': 4950.0, 'change': 24.0, 'changePercent': 0.49},
            'dow': {'price': 36400.0, 'change': 180.0, 'changePercent': 0.50},
            'nasdaq': {'price': 15800.0, 'change': 85.0, 'changePercent': 0.54},
            'timestamp': datetime.now().strftime('%Y-%m-%d'),
            'last_updated': datetime.now().isoformat(),
            'error': 'Using mock data due to API error'
        }
        return jsonify(mock_data)


@app.route('/api/featured-news')
def featured_news():
    """API endpoint to get featured news articles based on influence score."""
    try:
        from web.data.news_data import get_featured_news

        # Get query parameters
        limit = request.args.get('limit', default=6, type=int)

        # Get featured news articles
        featured_news_data = get_featured_news(limit=limit)

        return jsonify(featured_news_data)

    except Exception as e:
        logger.error(f"Error getting featured news: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/more-news')
def more_news():
    """API endpoint to get more news articles excluding featured ones."""
    try:
        from web.data.news_data import get_more_news

        # Get query parameters
        limit = request.args.get('limit', default=20, type=int)
        exclude_ids = request.args.getlist('exclude_ids')

        # Get more news articles
        more_news_data = get_more_news(limit=limit, exclude_ids=exclude_ids)

        return jsonify(more_news_data)

    except Exception as e:
        logger.error(f"Error getting more news: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/email/trigger-alert', methods=['POST'])
def trigger_email_alert():
    """API endpoint to trigger market alert (admin only)."""
    try:
        if not current_user.is_authenticated:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        if not data or 'title' not in data or 'message' not in data:
            return jsonify({'error': 'Title and message required'}), 400

        # Import here to avoid circular imports
        from web.email_service.scheduler import trigger_market_alert

        trigger_market_alert(
            title=data['title'],
            message=data['message'],
            severity=data.get('severity', 'medium')
        )

        return jsonify({'success': True, 'message': 'Market alert triggered'})

    except Exception as e:
        logger.error(f"Error triggering market alert: {e}")
        return jsonify({'error': str(e)}), 500


def is_port_in_use(host, port):
    """Check if a port is already in use."""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            s.bind((host, port))
            return False
    except socket.error:
        return True


def find_free_port(start_port, max_attempts=10):
    """Find a free port starting from start_port."""
    port = start_port
    for _ in range(max_attempts):
        if not is_port_in_use('0.0.0.0', port):
            return port
        port += 1
    return None


def main():
    """Entry point for the package."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Start the web server')
    parser.add_argument('--port', type=int, default=PORT,
                        help=f'Port to run the server on (default: {PORT})')
    parser.add_argument('--host', type=str, default=HOST,
                        help=f'Host to run the server on (default: {HOST})')
    parser.add_argument('--debug', action='store_true',
                        default=DEBUG, help='Run in debug mode')
    parser.add_argument('--no-reloader', action='store_true',
                        help='Disable the auto-reloader in debug mode')
    parser.add_argument('--force-port', action='store_true',
                        help='Force using the specified port even if it requires finding an alternative')
    args = parser.parse_args()

    # Check if the port is already in use
    if is_port_in_use(args.host, args.port):
        if args.force_port:
            # Try to find an alternative port
            free_port = find_free_port(args.port + 1)
            if free_port:
                logger.info(f"Using alternative port {free_port} instead")
                args.port = free_port
            else:
                logger.error(
                    "Could not find a free port. Please stop the existing server or specify a different port.")
                return 1
        else:
            logger.error(
                f"Port {args.port} is already in use. Please use --force-port to find an alternative port or specify a different port with --port.")
            return 1

    # Start the web server
    logger.info(f"Starting web server on {args.host}:{args.port}")
    try:
        # Configure Flask run options
        run_options = {
            'debug': args.debug,
            'host': args.host,
            'port': args.port
        }

        # If in debug mode, handle reloader settings
        if args.debug:
            if args.no_reloader:
                logger.info("Starting in debug mode with reloader disabled")
                run_options['use_reloader'] = False
            else:
                logger.info("Starting in debug mode with reloader enabled")
                run_options['threaded'] = False

        # Start the Flask app with the configured options
        app.run(**run_options)
        return 0
    except OSError as e:
        logger.error(f"Error starting server: {e}")
        return 1


@app.teardown_appcontext
def shutdown_session(exception=None):
    """Clean up database session after each request."""
    db.session.remove()


if __name__ == '__main__':
    main()
